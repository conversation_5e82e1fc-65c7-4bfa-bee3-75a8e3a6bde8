"""
Body Part Remover App
Removes specified body parts from images and outputs as PNG with transparency
"""

import cv2
import mediapipe as mp
import numpy as np
from PIL import Image, ImageTk
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os

class BodyPartRemover:
    def __init__(self):
        self.mp_pose = mp.solutions.pose
        self.mp_drawing = mp.solutions.drawing_utils
        self.pose = self.mp_pose.Pose(
            static_image_mode=True,
            model_complexity=2,
            enable_segmentation=True,
            min_detection_confidence=0.5
        )
        
        # Define body part regions based on pose landmarks
        self.body_parts = {
            'head': [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10],  # Face and head landmarks
            'left_arm': [11, 13, 15, 17, 19, 21],  # Left arm landmarks
            'right_arm': [12, 14, 16, 18, 20, 22],  # Right arm landmarks
            'torso': [11, 12, 23, 24],  # Shoulder and hip landmarks
            'left_leg': [23, 25, 27, 29, 31],  # Left leg landmarks
            'right_leg': [24, 26, 28, 30, 32]  # Right leg landmarks
        }
    
    def detect_pose(self, image):
        """Detect pose landmarks in the image"""
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.pose.process(rgb_image)
        return results
    
    def create_body_part_mask(self, image, landmarks, parts_to_remove):
        """Create a mask for the specified body parts to remove"""
        height, width = image.shape[:2]
        mask = np.zeros((height, width), dtype=np.uint8)
        
        if not landmarks:
            return mask
        
        # Convert landmarks to pixel coordinates
        landmark_points = []
        for landmark in landmarks.pose_landmarks.landmark:
            x = int(landmark.x * width)
            y = int(landmark.y * height)
            landmark_points.append((x, y))
        
        # Create masks for each body part to remove
        for part_name in parts_to_remove:
            if part_name in self.body_parts:
                part_landmarks = self.body_parts[part_name]
                part_points = [landmark_points[i] for i in part_landmarks 
                             if i < len(landmark_points)]
                
                if len(part_points) >= 3:
                    # Create convex hull around the body part
                    hull = cv2.convexHull(np.array(part_points))
                    cv2.fillPoly(mask, [hull], 255)
                elif len(part_points) > 0:
                    # For parts with few points, create circles around them
                    for point in part_points:
                        cv2.circle(mask, point, 30, 255, -1)
        
        return mask
    
    def remove_body_parts(self, image_path, parts_to_remove, output_path):
        """Remove specified body parts from image and save as PNG"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Could not load image")
            
            # Detect pose
            results = self.detect_pose(image)
            
            if not results.pose_landmarks:
                raise ValueError("No pose detected in image")
            
            # Create mask for body parts to remove
            mask = self.create_body_part_mask(image, results, parts_to_remove)
            
            # Convert image to RGBA
            image_rgba = cv2.cvtColor(image, cv2.COLOR_BGR2RGBA)
            
            # Apply mask (make masked areas transparent)
            image_rgba[mask == 255] = [0, 0, 0, 0]  # Transparent
            
            # Save as PNG
            pil_image = Image.fromarray(image_rgba, 'RGBA')
            pil_image.save(output_path, 'PNG')
            
            return True, "Successfully processed image"
            
        except Exception as e:
            return False, str(e)
    
    def preview_detection(self, image_path):
        """Preview pose detection on the image"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                return None, "Could not load image"
            
            results = self.detect_pose(image)
            
            if results.pose_landmarks:
                # Draw pose landmarks
                annotated_image = image.copy()
                self.mp_drawing.draw_landmarks(
                    annotated_image,
                    results.pose_landmarks,
                    self.mp_pose.POSE_CONNECTIONS
                )
                return annotated_image, "Pose detected successfully"
            else:
                return image, "No pose detected"
                
        except Exception as e:
            return None, str(e)


class BodyPartRemoverGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Body Part Remover")
        self.root.geometry("800x600")
        
        self.remover = BodyPartRemover()
        self.current_image_path = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # File selection
        file_frame = ttk.LabelFrame(main_frame, text="Image Selection", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(file_frame, text="Select Image", 
                  command=self.select_image).grid(row=0, column=0, padx=5)
        
        self.file_label = ttk.Label(file_frame, text="No image selected")
        self.file_label.grid(row=0, column=1, padx=10)
        
        # Body parts selection
        parts_frame = ttk.LabelFrame(main_frame, text="Body Parts to Remove", padding="5")
        parts_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        self.part_vars = {}
        parts = ['head', 'left_arm', 'right_arm', 'torso', 'left_leg', 'right_leg']
        
        for i, part in enumerate(parts):
            var = tk.BooleanVar()
            self.part_vars[part] = var
            ttk.Checkbutton(parts_frame, text=part.replace('_', ' ').title(), 
                           variable=var).grid(row=i//2, column=i%2, sticky=tk.W, padx=5, pady=2)
        
        # Preview frame
        preview_frame = ttk.LabelFrame(main_frame, text="Preview", padding="5")
        preview_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=(10, 0))
        
        self.preview_label = ttk.Label(preview_frame, text="No preview available")
        self.preview_label.grid(row=0, column=0)
        
        ttk.Button(preview_frame, text="Preview Detection", 
                  command=self.preview_detection).grid(row=1, column=0, pady=5)
        
        # Action buttons
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        ttk.Button(action_frame, text="Process Image", 
                  command=self.process_image).grid(row=0, column=0, padx=5)
        
        ttk.Button(action_frame, text="Clear Selection", 
                  command=self.clear_selection).grid(row=0, column=1, padx=5)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
    
    def select_image(self):
        """Select an image file"""
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff")]
        )
        
        if file_path:
            self.current_image_path = file_path
            self.file_label.config(text=os.path.basename(file_path))
            self.status_var.set(f"Image selected: {os.path.basename(file_path)}")
    
    def preview_detection(self):
        """Preview pose detection"""
        if not self.current_image_path:
            messagebox.showwarning("Warning", "Please select an image first")
            return
        
        self.status_var.set("Processing preview...")
        self.root.update()
        
        preview_image, message = self.remover.preview_detection(self.current_image_path)
        
        if preview_image is not None:
            # Resize image for preview
            height, width = preview_image.shape[:2]
            max_size = 300
            if width > height:
                new_width = max_size
                new_height = int(height * max_size / width)
            else:
                new_height = max_size
                new_width = int(width * max_size / height)
            
            preview_resized = cv2.resize(preview_image, (new_width, new_height))
            preview_rgb = cv2.cvtColor(preview_resized, cv2.COLOR_BGR2RGB)
            
            # Convert to PhotoImage
            pil_image = Image.fromarray(preview_rgb)
            photo = ImageTk.PhotoImage(pil_image)
            
            self.preview_label.config(image=photo, text="")
            self.preview_label.image = photo  # Keep a reference
            
            self.status_var.set(message)
        else:
            self.status_var.set(f"Error: {message}")
    
    def process_image(self):
        """Process the image and remove selected body parts"""
        if not self.current_image_path:
            messagebox.showwarning("Warning", "Please select an image first")
            return
        
        # Get selected body parts
        selected_parts = [part for part, var in self.part_vars.items() if var.get()]
        
        if not selected_parts:
            messagebox.showwarning("Warning", "Please select at least one body part to remove")
            return
        
        # Select output file
        output_path = filedialog.asksaveasfilename(
            title="Save Processed Image",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png")]
        )
        
        if not output_path:
            return
        
        self.status_var.set("Processing image...")
        self.root.update()
        
        success, message = self.remover.remove_body_parts(
            self.current_image_path, selected_parts, output_path
        )
        
        if success:
            messagebox.showinfo("Success", f"Image processed successfully!\nSaved to: {output_path}")
            self.status_var.set("Image processed successfully")
        else:
            messagebox.showerror("Error", f"Failed to process image: {message}")
            self.status_var.set(f"Error: {message}")
    
    def clear_selection(self):
        """Clear all selections"""
        for var in self.part_vars.values():
            var.set(False)
        self.status_var.set("Selection cleared")


def main():
    root = tk.Tk()
    app = BodyPartRemoverGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
