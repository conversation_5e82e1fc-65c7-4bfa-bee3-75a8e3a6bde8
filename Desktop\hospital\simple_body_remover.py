"""
Simple Body Part Remover App
Uses OpenCV for face detection and basic image processing
"""

import cv2
import numpy as np
from PIL import Image, ImageTk
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os

class SimpleBodyPartRemover:
    def __init__(self):
        # Load OpenCV's pre-trained face detection model
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.body_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_fullbody.xml')
        
    def detect_faces(self, image):
        """Detect faces in the image"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
        return faces
    
    def detect_bodies(self, image):
        """Detect full bodies in the image"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        bodies = self.body_cascade.detectMultiScale(gray, 1.1, 4)
        return bodies
    
    def create_mask_from_regions(self, image, regions, region_type='face'):
        """Create a mask for the specified regions"""
        height, width = image.shape[:2]
        mask = np.zeros((height, width), dtype=np.uint8)
        
        for (x, y, w, h) in regions:
            if region_type == 'face':
                # For faces, create a slightly larger elliptical mask
                center_x, center_y = x + w//2, y + h//2
                radius_x, radius_y = int(w * 0.6), int(h * 0.8)
                cv2.ellipse(mask, (center_x, center_y), (radius_x, radius_y), 0, 0, 360, 255, -1)
            elif region_type == 'upper_body':
                # For upper body, use the upper portion of detected body
                upper_h = int(h * 0.6)  # Upper 60% of body
                cv2.rectangle(mask, (x, y), (x + w, y + upper_h), 255, -1)
            elif region_type == 'lower_body':
                # For lower body, use the lower portion of detected body
                lower_start = int(h * 0.4)  # Start from 40% down
                cv2.rectangle(mask, (x, y + lower_start), (x + w, y + h), 255, -1)
            else:
                # Default: full rectangle
                cv2.rectangle(mask, (x, y), (x + w, y + h), 255, -1)
        
        return mask
    
    def remove_background_simple(self, image):
        """Simple background removal using edge detection and morphology"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply GaussianBlur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Edge detection
        edges = cv2.Canny(blurred, 50, 150)
        
        # Morphological operations to close gaps
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        edges = cv2.dilate(edges, kernel, iterations=2)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Create mask from largest contour (assuming it's the person)
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            mask = np.zeros(gray.shape, dtype=np.uint8)
            cv2.fillPoly(mask, [largest_contour], 255)
            
            # Smooth the mask
            mask = cv2.GaussianBlur(mask, (5, 5), 0)
            return mask
        
        return np.ones(gray.shape, dtype=np.uint8) * 255
    
    def process_image(self, image_path, parts_to_remove, output_path):
        """Process image and remove specified parts"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError("Could not load image")
            
            height, width = image.shape[:2]
            final_mask = np.ones((height, width), dtype=np.uint8) * 255  # Start with white (keep everything)
            
            # Process each part to remove
            for part in parts_to_remove:
                if part == 'head':
                    faces = self.detect_faces(image)
                    if len(faces) > 0:
                        part_mask = self.create_mask_from_regions(image, faces, 'face')
                        final_mask = cv2.bitwise_and(final_mask, cv2.bitwise_not(part_mask))
                
                elif part == 'upper_body':
                    bodies = self.detect_bodies(image)
                    if len(bodies) > 0:
                        part_mask = self.create_mask_from_regions(image, bodies, 'upper_body')
                        final_mask = cv2.bitwise_and(final_mask, cv2.bitwise_not(part_mask))
                
                elif part == 'lower_body':
                    bodies = self.detect_bodies(image)
                    if len(bodies) > 0:
                        part_mask = self.create_mask_from_regions(image, bodies, 'lower_body')
                        final_mask = cv2.bitwise_and(final_mask, cv2.bitwise_not(part_mask))
                
                elif part == 'background':
                    # Remove background, keep person
                    person_mask = self.remove_background_simple(image)
                    final_mask = cv2.bitwise_and(final_mask, person_mask)
            
            # Convert image to RGBA
            image_rgba = cv2.cvtColor(image, cv2.COLOR_BGR2RGBA)
            
            # Apply final mask
            alpha_channel = final_mask
            image_rgba[:, :, 3] = alpha_channel
            
            # Save as PNG
            pil_image = Image.fromarray(image_rgba, 'RGBA')
            pil_image.save(output_path, 'PNG')
            
            return True, "Successfully processed image"
            
        except Exception as e:
            return False, str(e)
    
    def preview_detection(self, image_path):
        """Preview detection results"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                return None, "Could not load image"
            
            preview_image = image.copy()
            
            # Draw face detections
            faces = self.detect_faces(image)
            for (x, y, w, h) in faces:
                cv2.rectangle(preview_image, (x, y), (x + w, y + h), (255, 0, 0), 2)
                cv2.putText(preview_image, 'Face', (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
            
            # Draw body detections
            bodies = self.detect_bodies(image)
            for (x, y, w, h) in bodies:
                cv2.rectangle(preview_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(preview_image, 'Body', (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            message = f"Detected {len(faces)} face(s) and {len(bodies)} body/bodies"
            return preview_image, message
            
        except Exception as e:
            return None, str(e)


class SimpleBodyRemoverGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Simple Body Part Remover")
        self.root.geometry("900x700")
        
        self.remover = SimpleBodyPartRemover()
        self.current_image_path = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # File selection
        file_frame = ttk.LabelFrame(main_frame, text="Image Selection", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(file_frame, text="Select Image", 
                  command=self.select_image).grid(row=0, column=0, padx=5)
        
        self.file_label = ttk.Label(file_frame, text="No image selected")
        self.file_label.grid(row=0, column=1, padx=10)
        
        # Parts selection
        parts_frame = ttk.LabelFrame(main_frame, text="Parts to Remove", padding="5")
        parts_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        self.part_vars = {}
        parts = [
            ('head', 'Head/Face'),
            ('upper_body', 'Upper Body'),
            ('lower_body', 'Lower Body'),
            ('background', 'Background (Keep Person)')
        ]
        
        for i, (part_key, part_label) in enumerate(parts):
            var = tk.BooleanVar()
            self.part_vars[part_key] = var
            ttk.Checkbutton(parts_frame, text=part_label, 
                           variable=var).grid(row=i, column=0, sticky=tk.W, padx=5, pady=2)
        
        # Preview frame
        preview_frame = ttk.LabelFrame(main_frame, text="Preview", padding="5")
        preview_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=(10, 0))
        
        self.preview_label = ttk.Label(preview_frame, text="No preview available")
        self.preview_label.grid(row=0, column=0)
        
        ttk.Button(preview_frame, text="Preview Detection", 
                  command=self.preview_detection).grid(row=1, column=0, pady=5)
        
        # Instructions
        instructions_frame = ttk.LabelFrame(main_frame, text="Instructions", padding="5")
        instructions_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        instructions_text = """
1. Select an image file containing a person
2. Preview detection to see what parts are detected
3. Choose which parts to remove (or select 'Background' to keep only the person)
4. Click 'Process Image' to create PNG with transparency
        """
        ttk.Label(instructions_frame, text=instructions_text.strip(), 
                 justify=tk.LEFT).grid(row=0, column=0, sticky=tk.W)
        
        # Action buttons
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=3, column=0, columnspan=2, pady=10)
        
        ttk.Button(action_frame, text="Process Image", 
                  command=self.process_image).grid(row=0, column=0, padx=5)
        
        ttk.Button(action_frame, text="Clear Selection", 
                  command=self.clear_selection).grid(row=0, column=1, padx=5)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Select an image to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
    
    def select_image(self):
        """Select an image file"""
        file_path = filedialog.askopenfilename(
            title="Select Image",
            filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff")]
        )
        
        if file_path:
            self.current_image_path = file_path
            self.file_label.config(text=os.path.basename(file_path))
            self.status_var.set(f"Image selected: {os.path.basename(file_path)}")
    
    def preview_detection(self):
        """Preview detection results"""
        if not self.current_image_path:
            messagebox.showwarning("Warning", "Please select an image first")
            return
        
        self.status_var.set("Processing preview...")
        self.root.update()
        
        preview_image, message = self.remover.preview_detection(self.current_image_path)
        
        if preview_image is not None:
            # Resize image for preview
            height, width = preview_image.shape[:2]
            max_size = 400
            if width > height:
                new_width = max_size
                new_height = int(height * max_size / width)
            else:
                new_height = max_size
                new_width = int(width * max_size / height)
            
            preview_resized = cv2.resize(preview_image, (new_width, new_height))
            preview_rgb = cv2.cvtColor(preview_resized, cv2.COLOR_BGR2RGB)
            
            # Convert to PhotoImage
            pil_image = Image.fromarray(preview_rgb)
            photo = ImageTk.PhotoImage(pil_image)
            
            self.preview_label.config(image=photo, text="")
            self.preview_label.image = photo  # Keep a reference
            
            self.status_var.set(message)
        else:
            self.status_var.set(f"Error: {message}")
    
    def process_image(self):
        """Process the image and remove selected parts"""
        if not self.current_image_path:
            messagebox.showwarning("Warning", "Please select an image first")
            return
        
        # Get selected parts
        selected_parts = [part for part, var in self.part_vars.items() if var.get()]
        
        if not selected_parts:
            messagebox.showwarning("Warning", "Please select at least one option")
            return
        
        # Select output file
        output_path = filedialog.asksaveasfilename(
            title="Save Processed Image",
            defaultextension=".png",
            filetypes=[("PNG files", "*.png")]
        )
        
        if not output_path:
            return
        
        self.status_var.set("Processing image...")
        self.root.update()
        
        success, message = self.remover.process_image(
            self.current_image_path, selected_parts, output_path
        )
        
        if success:
            messagebox.showinfo("Success", f"Image processed successfully!\nSaved to: {output_path}")
            self.status_var.set("Image processed successfully")
        else:
            messagebox.showerror("Error", f"Failed to process image: {message}")
            self.status_var.set(f"Error: {message}")
    
    def clear_selection(self):
        """Clear all selections"""
        for var in self.part_vars.values():
            var.set(False)
        self.status_var.set("Selection cleared")


def main():
    root = tk.Tk()
    app = SimpleBodyRemoverGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
