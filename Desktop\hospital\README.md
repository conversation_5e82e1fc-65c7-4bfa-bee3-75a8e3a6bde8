# Body Part Remover App

An application that can remove heads and other body parts from images and output them as PNG files with transparency.

## Features

- **Face Detection**: Uses OpenCV's Haar cascades to detect faces and bodies in images
- **Selective Removal**: Choose which body parts to remove (head, upper body, lower body)
- **Background Removal**: Option to remove background and keep only the person
- **PNG Output**: Outputs processed images as PNG files with transparent backgrounds where body parts were removed
- **Dual Interface**: Both GUI and command-line interfaces available
- **Preview Function**: Preview detection results before processing

## Installation

1. Make sure you have Python 3.7+ installed
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Simple GUI Application (Recommended)

Run the simple GUI application:

```bash
python simple_body_remover.py
```

1. Click "Select Image" to choose an image file
2. Use "Preview Detection" to see what parts are detected
3. Select which parts you want to remove using the checkboxes
4. Click "Process Image" to remove the selected parts and save as PNG

### Command Line Interface

For batch processing or scripting:

```bash
# Preview detections
python cli_body_remover.py your_image.jpg --preview

# Remove head
python cli_body_remover.py input.jpg output.png --remove head

# Remove background (keep person only)
python cli_body_remover.py input.jpg output.png --remove background
```

### Advanced GUI Application

For more advanced features (requires MediaPipe):

```bash
python body_part_remover.py
```

### Supported Body Parts

- **Head**: Face and head region
- **Left Arm**: Left shoulder, elbow, wrist, and hand
- **Right Arm**: Right shoulder, elbow, wrist, and hand
- **Torso**: Chest and upper body area
- **Left Leg**: Left hip, knee, ankle, and foot
- **Right Leg**: Right hip, knee, ankle, and foot

## How It Works

1. **Pose Detection**: The app uses MediaPipe's pose estimation to identify key body landmarks
2. **Region Mapping**: Body parts are mapped to specific landmark groups
3. **Mask Creation**: Creates masks around the selected body parts using convex hulls
4. **Transparency**: Applies the masks to make selected areas transparent
5. **PNG Export**: Saves the result as a PNG file with transparency

## Requirements

- Python 3.7+
- OpenCV
- MediaPipe
- Pillow (PIL)
- NumPy
- Matplotlib
- tkinter (usually included with Python)

## Limitations

- Works best with clear, well-lit images of people
- Requires the full body or at least the target body parts to be visible
- Accuracy depends on pose detection quality
- May not work well with heavily occluded or blurry images

## Tips for Best Results

- Use high-quality images with good lighting
- Ensure the person is clearly visible and not heavily occluded
- Images with the person facing the camera work best
- Avoid cluttered backgrounds when possible

## Troubleshooting

- **"No pose detected"**: Try with a clearer image or ensure the person is fully visible
- **Inaccurate removal**: The pose detection might be imperfect; try with different images
- **Installation issues**: Make sure all dependencies are installed correctly

## Future Enhancements

- Support for multiple people in one image
- More precise segmentation using advanced AI models
- Batch processing capabilities
- Custom body part selection tools
- Integration with more advanced background removal techniques
